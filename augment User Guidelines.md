我需要您帮助我优化和完善一套 Augment Agent 的用户指南。这些指南旨在改善 Augment Agent 在编程任务和项目中与我的协作方式。

请审查我提供的指南并通过以下方式增强它们：
1. 使它们更具体和可操作
2. 澄清任何模糊的语言
3. 添加有助于 Augment Agent 理解我偏好的缺失细节
4. 将它们组织成逻辑结构
5. 确保它们与 Augment Agent 的能力和最佳实践保持一致
6. 纠正任何技术不准确或不一致之处

增强的指南应该帮助 Augment Agent：
- 更好地理解我的编码风格和偏好
- 遵循我的项目特定约定
- 在建议代码更改时做出更明智的决策
- 在开发任务中更有效地协作

请以清晰、结构良好的格式提供优化的指南，使 Augment Agent 能够在我们的工作会话中轻松遵循和参考。

## 代码修改后审查指导原则

### 强制性代码清理检查

在完成任何代码修改后，Augment Agent 必须执行以下代码审查步骤，以确保代码库的整洁性和可维护性：

#### 1. 执行时机
- **必须在以下情况后执行**：完成任何代码修改、重构或功能实现后
- **执行顺序**：在认为任务完成之前的最后一步
- **适用范围**：所有类型的代码更改，包括但不限于新功能添加、bug修复、重构等

#### 2. 检查内容

**2.1 未使用的导入和依赖**
- 识别因代码更改而不再需要的 import 语句
- 检查项目依赖文件中可能变得多余的包
- 验证所有导入的模块、函数、类是否在代码中被实际使用

**2.2 废弃的函数、类和模块**
- 识别不再被调用的函数和方法
- 检查不再被实例化或引用的类
- 发现因重构而变得孤立的模块或文件

**2.3 死代码路径**
- 识别因逻辑更改而变得不可达的代码分支
- 检查永远不会被执行的条件语句
- 发现因参数或配置更改而失效的代码路径

**2.4 过时的配置和设置代码**
- 识别不再相关的配置文件或配置项
- 检查过时的环境设置或初始化代码
- 发现不再需要的常量定义或全局变量

#### 3. 必需操作

**3.1 识别和列出**
- 明确列出所有发现的废弃代码和模块
- 提供每个废弃项目的具体位置和原因说明
- 按影响程度和移除风险对发现的项目进行分类

**3.2 移除建议**
- 对于明确无用的代码，直接建议移除
- 对于可能有潜在价值但当前未使用的代码，标记为"待确认移除"
- 提供移除操作的具体步骤和潜在影响分析

**3.3 用户确认机制**
- **自动移除**：仅限于明显无害的未使用导入和明确的死代码
- **请求确认**：对于以下情况必须征求用户同意
  - 可能包含重要逻辑的未使用函数或类
  - 可能用于未来功能的预留代码
  - 涉及配置或设置的代码更改
  - 任何可能影响系统稳定性的移除操作

**3.4 文档更新**
- 如果移除的功能在文档中有说明，同步更新相关文档
- 更新代码注释中对已移除功能的引用
- 修订 README 或其他项目文档中的过时信息

#### 4. 执行流程

1. **自动扫描**：使用静态分析工具或手动检查识别潜在的废弃代码
2. **分类整理**：将发现的问题按类型和风险级别分组
3. **生成报告**：创建清晰的废弃代码清单，包含位置、类型和建议操作
4. **执行清理**：按照确认机制执行相应的清理操作
5. **验证测试**：确保清理操作不会破坏现有功能
6. **文档同步**：更新相关文档和注释

#### 5. 质量保证

- 在执行任何移除操作前，确保有适当的版本控制备份
- 建议在清理后运行完整的测试套件
- 对于大型清理操作，建议分批进行并逐步验证
- 保持清理操作的可追溯性，记录所有更改的原因和影响

这一指导原则确保每次代码修改后都能维持代码库的整洁性，避免技术债务的积累，并提高代码的可维护性和可读性。